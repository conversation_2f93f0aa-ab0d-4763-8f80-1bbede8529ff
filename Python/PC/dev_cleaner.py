import shutil
import subprocess
import sys
import time
import os

# 配置文件: 定义清理任务和超时设置
CONFIG = {
    "timeout": 300,  # 缓存清理超时时间 (5分钟)
    "tasks": {
        "homebrew": {"enabled": True},
        "pip": {"enabled": True},
        "uv": {"enabled": True},
        "npm": {"enabled": True},
        "yarn": {"enabled": False},
        "pnpm": {"enabled": False},
    }
}


class Colors:
    """终端颜色代码定义"""
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'


# 检测终端是否支持颜色显示
IS_COLOR_SUPPORTED = (sys.platform != "win32" or "WT_SESSION" in os.environ or os.getenv('TERM_PROGRAM') == 'vscode')


def colorize(text, color):
    """为文本添加颜色, 如果终端不支持则返回原文本"""
    return f"{color}{text}{Colors.ENDC}" if IS_COLOR_SUPPORTED else text


OS_PLATFORM = sys.platform  # 获取操作系统平台


def print_header(title):
    """打印格式化的标题头部"""
    print("\n" + "=" * 60)
    print(f"  {colorize(title, Colors.BOLD)}")
    print("=" * 60)


def print_status(name, status, duration, reason=""):
    """打印任务执行状态信息"""
    if status == "success":
        mark, color = "✓", Colors.GREEN
    elif status == "fail":
        mark, color = "✗", Colors.RED
    else:
        mark, color = "»", Colors.YELLOW

    time_str = f"({duration:.2f}s)"
    status_line = f"  [{colorize(mark, color)}] {name:.<40} {colorize(status.upper(), color)} {time_str}"
    print(status_line)
    if reason:
        print(f"      {colorize('└─ ' + reason, Colors.RED if status == 'fail' else Colors.YELLOW)}")


def format_size(size_bytes):
    """将字节数转换为人类可读的文件大小格式"""
    if size_bytes == 0: return "0 B"
    names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(names) - 1: size_bytes /= 1024.0; i += 1
    return f"{size_bytes:.1f} {names[i]}" if i > 0 else f"{int(size_bytes)} {names[i]}"


def get_disk_usage():
    """获取当前目录的可用磁盘空间"""
    try:
        return shutil.disk_usage('.').free
    except Exception:
        return 0


# --- 核心执行与工具函数 ---
def command_exists(cmd):
    """检查系统中是否存在指定命令"""
    return shutil.which(cmd) is not None


def run_command(cmd_list, timeout):
    """执行系统命令并返回结果"""
    is_windows = (OS_PLATFORM == "win32")
    command_to_run = " ".join(cmd_list) if is_windows else cmd_list  # Windows 需要字符串格式
    try:
        subprocess.run(
            command_to_run, shell=is_windows, capture_output=True, text=True,
            check=True, encoding='utf-8', timeout=timeout
        )
        return True, ""
    except FileNotFoundError:
        return False, f"命令 '{cmd_list[0]}' 未找到"
    except subprocess.TimeoutExpired:
        return False, f"命令执行超时 (超过 {timeout} 秒)"
    except subprocess.CalledProcessError as e:
        return False, f"命令执行失败: {e.stderr.strip()}"
    except Exception as e:
        return False, f"发生未知错误: {e}"


# --- 各种包管理器的清理函数 ---
def clean_homebrew(timeout):
    """清理 Homebrew 缓存"""
    if not command_exists("brew"): return None, "(未安装)"
    return run_command(["brew", "cleanup"], timeout)


def clean_pip(timeout):
    """清理 pip 缓存"""
    cmd = "py" if OS_PLATFORM == "win32" else "python3"  # Windows 使用 py 命令
    if not command_exists(cmd): return None, f"({cmd} 命令未找到)"
    return run_command([cmd, "-m", "pip", "cache", "purge"], timeout)


def clean_uv(timeout):
    """清理 uv 缓存"""
    if not command_exists("uv"): return None, "(未安装)"
    return run_command(["uv", "cache", "clean"], timeout)


def clean_npm(timeout):
    """清理 npm 缓存"""
    if not command_exists("npm"): return None, "(未安装)"
    return run_command(["npm", "cache", "verify"], timeout)


def clean_yarn(timeout):
    """清理 yarn 缓存"""
    if not command_exists("yarn"): return None, "(未安装)"
    return run_command(["yarn", "cache", "clean"], timeout)


def clean_pnpm(timeout):
    """清理 pnpm 缓存"""
    if not command_exists("pnpm"): return None, "(未安装)"
    return run_command(["pnpm", "store", "prune"], timeout)


# --- 主函数: 执行所有清理任务 ---
def main():
    """主函数: 遍历配置中的清理任务并执行"""
    # 任务名称到清理函数的映射
    task_map = {
        "homebrew": clean_homebrew, "pip": clean_pip, "uv": clean_uv,
        "npm": clean_npm, "yarn": clean_yarn, "pnpm": clean_pnpm,
    }

    # 记录清理前的磁盘空间
    free_space_before = get_disk_usage()
    print_header(f"开发者缓存清理 (当前磁盘可用: {colorize(format_size(free_space_before), Colors.BLUE)})")

    timeout = CONFIG["timeout"]

    # 遍历所有配置的清理任务
    for name, config in CONFIG["tasks"].items():
        if not config["enabled"]: continue  # 跳过未启用的任务
        if name == "homebrew" and OS_PLATFORM != 'darwin': continue  # macOS 专用

        print(f"\n{colorize('»', Colors.YELLOW)} 正在执行 {name.capitalize()} 的清理任务...")

        start_time = time.monotonic()  # 记录开始时间
        success, _ = None, ""
        try:
            # 调用对应的清理函数
            success, reason = task_map[name](timeout)
        except Exception as e:
            success, reason = False, f"脚本内部错误: {e}"
        duration = time.monotonic() - start_time  # 计算执行时间

        # 根据执行结果显示状态
        if success is None:  # None表示跳过 (如工具未安装)
            print_status(name.capitalize(), 'skipped', duration, reason)
        elif success:
            print_status(name.capitalize(), 'success', duration)
        else:
            print_status(name.capitalize(), 'fail', duration, reason)

    # 计算释放的磁盘空间
    free_space_after = get_disk_usage()
    space_freed = free_space_after - free_space_before
    if abs(space_freed) < 1024 * 1024: space_freed = 0  # 小于 1MB 视为无变化

    # 显示最终结果
    if space_freed > 0:
        final_message = f"清理完成! 释放空间: {colorize(format_size(space_freed), Colors.GREEN)}, 剩余: {colorize(format_size(free_space_after), Colors.BLUE)}"
    else:
        final_message = f"清理完成! (剩余磁盘空间: {colorize(format_size(free_space_after), Colors.BLUE)})"

    print_header(final_message)


if __name__ == "__main__":
    main()
