from seatable_api import Base, context, dateutils
import logging
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# 配置提醒天数
REMINDER_DAYS = [15, 7, 3]

# 配置状态
STATUS_COLUMN = '状态'
STATUS_ACTIVE = '订阅中'
STATUS_RENEWAL = '续订期'
STATUS_EXPIRED = '已过期'


def setup_logger():
    """
    配置日志记录器, 设置日志格式和级别
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_http_session():
    """
    创建带有重试机制的 HTTP 会话
    包含以下特性:
    1. 最大重试次数: 3次
    2. 重试间隔: 1秒
    3. 特定状态码重试: 500, 502, 503, 504
    """
    session = requests.Session()
    retry_strategy = Retry(
        total=3,  # 重试次数
        backoff_factor=1,  # 重试间隔(秒)
        status_forcelist=[500, 502, 503, 504]  # 重试状态码
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    return session


def format_date(date_str):
    """
    格式化日期字符串

    参数:
        date_str: ISO 8601 格式的日期字符串

    返回:
        str: YYYY-MM-DD 格式的日期字符串
    """
    try:
        # 如果日期包含 T 则取 T 之前的部分
        if 'T' in date_str:
            return date_str.split('T')[0]
        return date_str
    except Exception as e:
        logger.error(f"日期格式化时发生了错误: {str(e)}")
        return date_str


def get_full_service_name(vendor, service):
    """
    获取完整的服务名称, 包含厂商信息

    参数:
        vendor: 厂商名称
        service: 服务名称

    返回:
        str: 格式化后的完整服务名称
    """
    if not vendor:
        return f"[{service}]"
    return f"[{vendor} {service}]"


def update_service_status(base, table_name, row_id, vendor, service_name, new_status, current_status):
    """
    更新服务状态
    仅当当前状态与目标状态不同时才进行更新

    参数:
        base: SeaTable Base 对象
        table_name: 表名
        row_id: 行 ID
        vendor: 厂商名称
        service_name: 服务名称
        new_status: 新状态
        current_status: 当前状态

    返回:
        bool: 更新是否成功
    """
    full_name = get_full_service_name(vendor, service_name)

    if current_status == new_status:
        logger.info(f"-- {full_name} 因当前状态已经是 [{new_status}] 而无需更新")
        return True

    try:
        base.update_row(table_name, row_id, {STATUS_COLUMN: new_status})
        logger.info(f"-- 将 {full_name} 状态从 [{current_status}] 更新为 [{new_status}]")
        return True
    except Exception as e:
        logger.error(f"-- 为 {full_name} 更新状态时发生了错误: {str(e)}")
        return False


def send_notification(vendor, service_name, renewal_date, days_left=None, session=None, is_expired=False):
    """
    发送通知到指定的 API
    支持两种类型的通知: 续订提醒和过期提醒

    参数:
        vendor: 厂商名称
        service_name: 服务名称
        renewal_date: 续订日期
        days_left: 剩余天数 (续订提醒时使用)
        session: HTTP 会话对象
        is_expired: 是否为过期通知

    返回:
        bool: 发送是否成功
    """
    base_url = "https://sctapi.ftqq.com/SCT145062TA-uYpQvsRzTrirHQkgYVIK27OF.send"
    full_name = get_full_service_name(vendor, service_name)

    if is_expired:
        title = f"{full_name} 已到期提醒"
        desp = f"{full_name} 的续订时间为 [{renewal_date}], 目前已到期, 请关注!"
    else:
        title = f"{full_name} 续订提醒"
        desp = f"{full_name} 的续订时间为 [{renewal_date}], 距离续订还有 [{days_left}] 天, 请关注!"

    params = {'title': title, 'desp': desp}

    try:
        response = session.post(
            base_url,
            params=params,
            headers={'Content-Type': 'application/json;charset=utf-8'},
            timeout=3  # 超时时间(秒)
        )
        response.raise_for_status()
        logger.info(f"-- 为 {full_name} 发送通知已完成")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"-- 为 {full_name} 发送通知时发生了错误: {str(e)}")
        return False


def get_all_subscriptions(base, table_name):
    """
    获取表中所有服务记录

    参数:
        base: SeaTable Base 对象
        table_name: 表名

    返回:
        list: 所有服务记录列表
    """
    try:
        rows = base.list_rows(table_name)
        logger.info(f"-- 找到 [{len(rows)}] 个服务")
        return rows
    except Exception as e:
        logger.error(f"-- 获取服务时发生错误: {str(e)}")
        return []


def should_send_reminder(days_until_renewal):
    """
    判断是否需要发送提醒

    参数:
        days_until_renewal: 距离续订的天数

    返回:
        bool: 是否需要发送提醒
    """
    return days_until_renewal in REMINDER_DAYS


def check_renewals():
    """
    检查所有服务的续订时间并发送通知
    主要功能:
    1. 获取所有服务记录
    2. 检查每个服务的续订时间
    3. 根据情况发送通知和更新状态
    """
    try:
        logger.info("现在开始续订检查...")
        logger.info(f"在续订前 {REMINDER_DAYS} 天会分别发送续订提醒")

        # 初始化 SeaTable 连接
        base = Base(context.api_token, context.server_url)
        base.auth()

        table_name = context.current_table
        logger.info(f"正在检查表 [{table_name}]")

        # 获取所有服务
        rows = get_all_subscriptions(base, table_name)
        if not rows:
            logger.info("没有找到需要处理的服务")
            return

        # 创建 HTTP 会话
        session = create_http_session()
        today = dateutils.now()  # 使用 dateutils 获取当前时间
        logger.info(f"-- 当前时间 [{today}]")

        notifications_sent = 0
        status_updates = 0

        # 处理每个服务
        for row in rows:
            vendor = row.get('厂商', '未知厂商')
            service_name = row.get('服务', '未知服务')
            renewal_date_str = row.get('续订时间')
            current_status = row.get(STATUS_COLUMN)
            full_name = get_full_service_name(vendor, service_name)

            if not renewal_date_str:
                logger.debug(f"-- {full_name} 没有找到续订时间")
                continue

            # 格式化日期显示
            formatted_date = format_date(renewal_date_str)
            logger.info(f"-- 开始处理 {full_name} 的续订时间 [{formatted_date}]")

            # 计算距离续订的天数
            days_until_renewal = dateutils.datediff(
                start=today,
                end=renewal_date_str,
                unit='D'
            )
            logger.info(f"-- {full_name} 距离续订还有 [{days_until_renewal}] 天")

            # 检查是否已过期
            if days_until_renewal < 0:
                # 无论状态如何, 都发送已过期通知
                if send_notification(vendor, service_name, formatted_date, session=session, is_expired=True):
                    notifications_sent += 1

                # 仅当状态不是已过期时更新状态
                if current_status != STATUS_EXPIRED:
                    if update_service_status(base, table_name, row['_id'],
                                             vendor, service_name, STATUS_EXPIRED, current_status):
                        status_updates += 1
                continue

            # 检查是否需要发送续订提醒
            if should_send_reminder(days_until_renewal):
                # 无论状态如何, 都发送续订提醒
                if send_notification(vendor, service_name, formatted_date, days_until_renewal, session):
                    notifications_sent += 1

                # 仅当状态不是续订期时更新状态
                if current_status != STATUS_RENEWAL:
                    if update_service_status(base, table_name, row['_id'],
                                             vendor, service_name, STATUS_RENEWAL, current_status):
                        status_updates += 1

        # 输出统计信息
        logger.info(f"[{len(rows)}] 项服务的续订检查现已完成")
        logger.info(f"发送了 [{notifications_sent}] 条通知")
        logger.info(f"更新了 [{status_updates}] 个服务状态")

    except Exception as e:
        logger.error(f"续订检查时发生了错误: {str(e)}")
        raise


# 初始化日志记录器
logger = setup_logger()

# 主程序入口
if __name__ == "__main__":
    check_renewals()
