from seatable_api import Base, context
import logging

# 配置状态
STATUS_COLUMN = '状态'
STATUS_ACTIVE = '订阅中'
STATUS_RENEWAL = '续订期'
STATUS_EXPIRED = '已过期'

def setup_logger():
    """
    配置日志记录器
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def should_update_status(current_status):
    """
    判断是否需要更新状态
    """
    return current_status in [STATUS_RENEWAL, STATUS_EXPIRED]

def update_total():
    """
    更新触发自动化的行的总金额、订阅次数和状态, 并显示简单的 toast 通知
    """
    try:
        logger = setup_logger()
        logger.info("开始更新数据...")

        # 获取触发行的信息
        current_row = context.current_row
        current_table = context.current_table

        if not current_row or not current_table:
            logger.error("缺失当前行或表信息")
            return

        # 初始化连接
        base = Base(context.api_token, context.server_url)
        base.auth()

        # 获取当前值
        row_id = current_row['_id']
        service_name = current_row.get('服务', '未知服务')
        current_price = float(current_row.get('最近支付') or 0.00)
        current_total = float(current_row.get('总金额') or 0.00)
        current_subscription_count = int(current_row.get('订阅次数') or 0)
        current_status = current_row.get(STATUS_COLUMN)

        # 计算新的值
        new_total = current_total + current_price
        new_subscription_count = current_subscription_count + 1

        # 准备更新数据
        update_data = {
            '总金额': round(new_total, 2),  # 确保总金额保留两位小数
            '订阅次数': new_subscription_count
        }

        # 检查是否需要更新状态
        if should_update_status(current_status):
            update_data[STATUS_COLUMN] = STATUS_ACTIVE
            logger.info(f"-- [{service_name}] 状态将从 [{current_status}] 更新为 [{STATUS_ACTIVE}]")

        # 更新数据
        base.update_row(current_table, row_id, update_data)

        # 记录更新信息
        logger.info(f"-- [{service_name}] 原始金额 [{current_total:.2f}] 新增 [{current_price:.2f}] 最新 [{new_total:.2f}]")
        logger.info(f"-- [{service_name}] 更新订阅次数为 [{new_subscription_count}]")
        logger.info(f"成功更新表 [{current_table}] 中行 [{row_id}] 数据")

        # 发送简单的 toast 通知
        base.send_toast_notification(
            username='<EMAIL>',
            msg="自动化程序成功更新了记录",
            toast_type='success'
        )

    except Exception as e:
        logger.error(f"发生了错误: {str(e)}")
        logger.error(f"错误类型: {type(e)}")

# 执行更新函数
if __name__ == "__main__":
    update_total()