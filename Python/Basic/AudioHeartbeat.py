import time
import numpy as np
import sounddevice as sd
import sys

from datetime import datetime
from rich.console import Console

# ==============================================================================
# 核心配置 (Core Configuration)
# ==============================================================================

# 时间计划配置 (24小时制精确到秒)
SCHEDULE = [
    ("08:49:00", "08:51:00"),
    ("11:49:00", "11:51:00"),
    ("12:58:00", "13:00:00")
]

# 音频模式配置
AUDIO_CONFIG = {
    "mode": "silent",  # 可选: "silent" (静音模式) 或 "tone" (测试音模式)
    "waveform": "sine",  # 可选测试不同波形: "sine" (纯净的正弦波), "triangle" (更柔和的三角波)
    "tone_frequency_hz": 440,  # 测试音频率 (Hz)
    "tone_amplitude": 0.2  # 测试音音量 (0.0 到 1.0). 0.5 是一个安全且清晰的音量.
}

# 运行参数配置
IDLE_SLEEP_INTERVAL = 3  # 在非活动时间, 每隔多少秒检查一次时间.
RETRY_DELAY = 6  # 当音频输出设备丢失或被占用后, 等待多少秒再尝试.

# ==============================================================================
# 常量定义 (Constants)
# ==============================================================================
# 用于操作系统与音频输出设备保活, 每隔一段时间发送音频数据到音频输出设备.

RATE = 48000
"""
采样率 (Sample Rate), 单位为赫兹 (Hz).
指的是每秒钟对声音信号的采样次数. 48000Hz 是 DVD 音质的国际标准.
右键点击任务栏右下角的音量图标, 选择“声音”. 在“播放”选项卡中, 选择当前音频输出设备, 点击“属性”.
在“高级”选项卡中可以看到音频输出设备的默认格式.
"""

CHANNELS = 1  # 静音流用单声道即可, 更节省资源. 如需立体声请使用 2.
"""
声道数 (Number of Channels)
1 表示单声道 (Mono), 2 表示立体声 (Stereo).
"""

CHUNK = 1024
"""
缓冲块大小 (Buffer/Chunk Size)
指每次从音频流中读取或写入的“帧数”(frames). 在一帧中,
包含的采样点数量与声道数相同. 这个值影响延迟和CPU使用率,
1024 或 2048 是一个常用的、较为均衡的值.
"""

# ==============================================================================
# 初始化与函数定义 (Initialization and Functions)
# ==============================================================================

# [Rich 初始化]
# 主控制台, 用于标准输出 (stdout)
console = Console()
# 创建一个专门用于标准错误 (stderr) 输出的控制台
console_stderr = Console(file=sys.stderr)

# 用于生成连续正弦波/三角波的全局相位/索引 (非常重要)
g_start_index = 0

def is_in_schedule(schedule_list):
    """检查当前时间是否在任何一个预设的时间段内"""
    now_time = datetime.now().time()
    for start_str, end_str in schedule_list:
        start_time = datetime.strptime(start_str, "%H:%M:%S").time()
        end_time = datetime.strptime(end_str, "%H:%M:%S").time()
        if start_time <= now_time < end_time:
            return True
    return False

def get_default_device_info():
    """
    智能获取 Windows 默认输出设备信息.
    优先使用 WASAPI 接口获取最准确的物理设备名称.
    如果失败, 则回退到通用的默认设备查询方法.
    """
    # 使用 with console.status(...) 替代静态打印, 并指定动画样式.
    with console.status("[cyan]正在查找 Windows 默认音频输出设备...[/]", spinner="dots"):
        try:
            # 增加一个短暂的延时, 确保动画有足够的时间显示, 优化用户体验.
            time.sleep(1)

            # sounddevice.default.device 是一个元组 (输入索引, 输出索引)
            device_index = sd.default.device[1]
            device_info = sd.query_devices(device_index, 'output')
            device_name = device_info['name']
            console.print(f"[green]✅ 已找到默认设备: '[bold]{device_name}[/bold]'[/]")
            return device_index, device_name
        except Exception as e:
            console.print(f"[red]❌ [bold]错误[/bold] 未找到任何默认设备: {e}[/]")
            return None, None

def find_next_schedule(schedule_list):
    """
    从计划列表中找出下一个即将到来的时间段.
    返回 (开始时间字符串, 结束时间字符串).
    """
    if not schedule_list: return None, None
    sorted_schedule = sorted(schedule_list, key=lambda item: datetime.strptime(item[0], "%H:%M:%S").time())
    now_time = datetime.now().time()
    for start_str, end_str in sorted_schedule:
        start_time = datetime.strptime(start_str, "%H:%M:%S").time()
        if start_time > now_time:
            return start_str, end_str
    return sorted_schedule[0][0], sorted_schedule[0][1]

# 使用回调函数作为新的高质量音频引擎
def audio_callback(outdata, frames, time, status):
    """
    该函数由 sounddevice 在后台自动、准时地调用.
    它负责填充音频缓冲区 `outdata`, 长度为 `frames`.
    """
    global g_start_index
    if status:
        # 使用专为 stderr 创建的 console_stderr 对象来打印
        console_stderr.print(f"[yellow]⚠️ 音频流警告: {status}[/]")

    # 根据模式生成音频数据
    if AUDIO_CONFIG["mode"] == "tone":
        # 生成测试音数据
        freq = AUDIO_CONFIG["tone_frequency_hz"]
        amp = AUDIO_CONFIG["tone_amplitude"]
        # 通过累加的 g_start_index 保证相位连续, 消除咔哒声.
        t = (g_start_index + np.arange(frames)) / float(RATE)

        # 根据波形选择生成不同的声音
        if AUDIO_CONFIG["waveform"] == "triangle":
            wave = (2 * amp / np.pi) * np.arcsin(np.sin(2 * np.pi * freq * t))
        else:  # 默认为 sine (正弦波)
            wave = amp * np.sin(2 * np.pi * freq * t)

        # sounddevice 需要 NumPy 数组有正确的形状, reshape(-1, 1) 用于单声道
        outdata[:] = wave.astype(np.float32).reshape(-1, CHANNELS)

    else:  # silent 模式
        outdata.fill(0)

    # 更新全局样本索引, 为下一次回调做准备
    g_start_index += frames


# ==============================================================================
# 主程序逻辑 (Main Logic)
# ==============================================================================
try:
    # 启动时清空终端内容
    console.clear()

    # 启动时打印核心配置信息
    console.rule("[bold bright_yellow]音频输出设备唤醒程序已启动[/]", style="bright_yellow")
    console.print(f"[cyan]▶[/] [bold]音频模式:[/bold] {AUDIO_CONFIG['mode']}")
    console.print(f"[cyan]▶[/] [bold]采样率:[/bold] {RATE} Hz")
    console.print("[cyan]▶[/] [bold]时间计划:[/bold]")
    for start, end in SCHEDULE:
        console.print(f"   - {start} -> {end}")
    console.rule(style="bright_yellow")

    # 引入一个状态旗标用于记忆上一次循环的状态
    was_active_in_last_iteration = False
    stream = None  # 在循环外定义 stream 变量

    # 使用一个永不退出的守护循环
    while True:
        # 在每次大循环开始时, 检查当前时间是否在预定计划内.
        is_active_now = is_in_schedule(SCHEDULE)

        if is_active_now:
            # 处于活动时间段
            if not was_active_in_last_iteration:
                # 仅在从休眠 -> 活动的瞬间, 重置相位索引, 确保每次播放都是从头开始.
                g_start_index = 0
                console.print("[yellow]⏰ 当前在预定时间计划内, 准备启动音频流...[/]")

                try:
                    # 自动查找默认设备
                    output_device_index, output_device_name = get_default_device_info()
                    if output_device_index is None:
                        raise RuntimeError("无法找到音频输出设备")

                    # 创建并启动 sounddevice 音频流
                    stream = sd.OutputStream(
                        samplerate=RATE,
                        blocksize=CHUNK,
                        device=output_device_index,
                        channels=CHANNELS,
                        dtype=np.float32,  # 直接指定数据类型
                        callback=audio_callback  # 注册我们的回调函数
                    )
                    stream.start()

                    # 根据配置决定日志信息
                    stream_type_msg = "测试音" if AUDIO_CONFIG["mode"] == "tone" else "静音"
                    waveform_msg = f" (波形: {AUDIO_CONFIG['waveform']})" if AUDIO_CONFIG["mode"] == "tone" else ""
                    console.print(
                        f"[green]🚀 成功获取设备: '[bold]{output_device_name}[/bold]', 准备开始播放[bold]{stream_type_msg}[/bold]流{waveform_msg}...[/]")

                    was_active_in_last_iteration = True

                except Exception as e:
                    console.print(f"[red]🔥 [bold]错误[/bold] 启动音频流失败: {e}[/]")
                    retry_message = f"[yellow]将在 [{RETRY_DELAY}] 秒后重试...[/]"
                    with console.status(retry_message, spinner="growHorizontal"):
                        time.sleep(RETRY_DELAY)
                    continue

            # 主线程只需在这里等待, 音频在后台由回调函数稳定生成.
            time.sleep(0.5)

        else:
            # 处于非活动时间段
            if was_active_in_last_iteration:
                console.print("[yellow]🌙 当前已超出预定时间计划, 现在进入休眠模式.[/]")
                # 停止并关闭 sounddevice 流
                if stream is not None:
                    stream.stop()
                    stream.close()
                    stream = None
                was_active_in_last_iteration = False

            next_start, next_end = find_next_schedule(SCHEDULE)
            display_message = f"下次运行时间: [bold bright_cyan]{next_start} - {next_end}[/]" if next_start else "未配置任何时间计划"

            # 使用 console.status 实现完美的单行临时状态显示, 替代原 sys.stdout.write
            with console.status(f"[dim]休眠中...{display_message}[/]", spinner="moon"):
                time.sleep(IDLE_SLEEP_INTERVAL)

except KeyboardInterrupt:
    console.print("[yellow]🛑 程序已由用户停止[/]")
except Exception as e:
    console.print(f"[bold red]💥 发生无法恢复的严重错误: {e}[/]")
finally:
    # 确保程序退出时 sounddevice 流被正确关闭
    if 'stream' in locals() and stream is not None and not stream.closed:
        stream.stop()
        stream.close()
    console.print("[green]✅ 资源已完全释放[/]")