# ==================== 工作空间级别参数 ====================

# 计费模式选择
data "coder_parameter" "instance_charge_type" {
  name         = "instance_charge_type"
  display_name = "计费模式"
  type         = "string"
  default      = "SPOTPAID"
  mutable      = false
  order        = 1
  icon         = "/icon/widgets.svg"

  option {
    name  = "竞价实例"
    value = "SPOTPAID"
  }
  option {
    name  = "按量计费"
    value = "POSTPAID_BY_HOUR"
  }
}

# 实例规格选择
data "coder_parameter" "instance_type" {
  name         = "instance_type"
  display_name = "实例规格"
  type         = "string"
  default      = "S5.LARGE8"
  mutable      = false
  order        = 2
  icon         = "/icon/widgets.svg"

  option {
    name  = "标准型 S5.LARGE8 (4C8G)"
    value = "S5.LARGE8"
    icon  = "/icon/memory.svg"
  }
  option {
    name  = "计算型 C4.2XLARGE16 (8C16G)"
    value = "C4.2XLARGE16"
    icon  = "/icon/memory.svg"
  }
}

# 竞价实例最高价格 (仅选择 竞价实例 时有效)
data "coder_parameter" "spot_max_price" {
  name         = "spot_max_price"
  display_name = "竞价实例最高出价 (元/小时)"
  description  = "请直接参考腾讯云 CVM 购买页的价格"
  type         = "number"
  default      = "0.048"
  mutable      = false
  order        = 3
  icon         = "/icon/widgets.svg"
}

# ==================== 模板级别的固定配置 ====================
# 通过本地变量设置的固定参数, 所有工作空间共享.

locals {
  # 地域和可用区配置 (固定)
  region            = "ap-hongkong"   # 地域: 香港地域
  availability_zone = "ap-hongkong-2" # 可用区: 香港二区

  # 镜像配置 (固定)
  image_id = "img-5s7vueks" # 镜像: 自定义镜像 Debian 13

  # 系统盘配置 (固定)
  system_disk_type = "CLOUD_PREMIUM" # 系统盘类型: 高性能云硬盘 (更多磁盘类型参见: https://registry.terraform.io/providers/tencentcloudstack/tencentcloud/latest/docs/resources/cbs_storage#storage_type-1)
  system_disk_size = 20              # 系统盘容量: 20GB

  # 网络配置 (固定)
  vpc_id            = "vpc-kpqmh20m"    # 私有网络 ID
  subnet_id         = "subnet-d7k6whm5" # 子网 ID
  security_group_id = "sg-9h3craf3"     # 安全组 ID

  # 公有 IP 配置 (固定)
  allocate_public_ip         = true                       # 自动分配公网 IP
  internet_charge_type       = "TRAFFIC_POSTPAID_BY_HOUR" # 按流量计费
  internet_max_bandwidth_out = 200                        # 200Mbps 带宽上限

  # SSH 密钥配置 (固定)
  ssh_key_id = "skey-g266lnub" # 自定义上传的 SSH 密钥

  # 项目配置
  project_id = 0 # 默认项目 ID
}

# ==================== 腾讯云 CVM 认证参数 ====================
# CVM 实例管理认证信息

variable "tencentcloud_cvm_secret_id" {
  description = "腾讯云 CVM SecretId"
  type        = string
  sensitive   = true
}

variable "tencentcloud_cvm_secret_key" {
  description = "腾讯云 CVM SecretKey"
  type        = string
  sensitive   = true
}

# ==================== JuiceFS 云服务参数 ====================
# JuiceFS 文件系统配置信息

variable "juicefs_vol_name" {
  description = "JuiceFS 文件系统名称"
  type        = string
  default     = "coder-code"
}

variable "juicefs_token" {
  description = "JuiceFS 云服务访问令牌"
  type        = string
  sensitive   = true
}

# ==================== 腾讯云 COS 存储参数 ====================
# COS 对象存储配置信息

variable "tencentcloud_cos_secret_id" {
  description = "腾讯云 COS SecretId"
  type        = string
  sensitive   = true
}

variable "tencentcloud_cos_secret_key" {
  description = "腾讯云 COS SecretKey"
  type        = string
  sensitive   = true
}

variable "tencentcloud_cos_endpoint" {
  description = "腾讯云 COS Endpoint"
  type        = string
  default     = "http://code-1257719787.cos.ap-hongkong.myqcloud.com"
}

# ==================== 工作空间信息 ====================
# Coder 工作空间元数据
data "coder_workspace" "me" {}
data "coder_workspace_owner" "me" {}
