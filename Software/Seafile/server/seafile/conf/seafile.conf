[fileserver]
port = 8082

[database]
type = mysql
host = seafile-db
port = 3306
user = seafile
password = XAVbWwARqTDKH9aCYYTtuLiAPhQQYW8seNFv8QzrNw62sDrALB66BE9kYVMFYvwi
db_name = seafile_db
connection_charset = utf8

[memcached]
memcached_options = --SERVER=seafile-memcached --POOL-MIN=10 --POOL-MAX=100

[commit_object_backend]
name = s3
bucket = seafile-commits-1257719787
key_id = AKIDITSjiimjXFl5FUiqsrnePJi5Pg7afq0K
key = TwLkKDIXm7V2UxV23LpfgVDtaL7p4VuQ
use_v4_signature = true
aws_region = ap-beijing
host = cos.ap-beijing.myqcloud.com
use_https = false

[fs_object_backend]
name = s3
bucket = seafile-fs-1257719787
key_id = AKIDITSjiimjXFl5FUiqsrnePJi5Pg7afq0K
key = TwLkKDIXm7V2UxV23LpfgVDtaL7p4VuQ
use_v4_signature = true
aws_region = ap-beijing
host = cos.ap-beijing.myqcloud.com
use_https = false

[block_backend]
name = s3
bucket = seafile-blocks-1257719787
key_id = AKIDITSjiimjXFl5FUiqsrnePJi5Pg7afq0K
key = TwLkKDIXm7V2UxV23LpfgVDtaL7p4VuQ
use_v4_signature = true
aws_region = ap-beijing
host = cos.ap-beijing.myqcloud.com
use_https = false

[notification]
enabled = true
host = seafile-notification
port = 8083
