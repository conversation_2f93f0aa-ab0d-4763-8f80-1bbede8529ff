# -*- coding: utf-8 -*-
SECRET_KEY = "@_oi#hv-$*5l)5w)@q-9#ro5&e=9958)*1t^qi-dae6%1vt#r2"
SERVICE_URL = "https://box.lilh.net"
CSRF_TRUSTED_ORIGINS = ["https://box.lilh.net"]

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'seahub_db',
        'USER': 'seafile',
        'PASSWORD': 'XAVbWwARqTDKH9aCYYTtuLiAPhQQYW8seNFv8QzrNw62sDrALB66BE9kYVMFYvwi',
        'HOST': 'seafile-db',
        'PORT': '3306',
        'OPTIONS': {'charset': 'utf8mb4'},
    }
}


CACHES = {
    'default': {
        'BACKEND': 'django_pylibmc.memcached.PyLibMCCache',
        'LOCATION': 'seafile-memcached:11211',
    },
    'locmem': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    },
}
COMPRESS_CACHE_BACKEND = 'locmem'

TIME_ZONE = 'Asia/Shanghai'
FILE_SERVER_ROOT = 'https://box.lilh.net/seafhttp'
