/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.11.13-MariaD<PERSON>, for debian-linux-gnu (x86_64)
--
-- Host: localhost    Database: seafile_db
-- ------------------------------------------------------
-- Server version	10.11.13-MariaDB-ubu2204

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Branch`
--

DROP TABLE IF EXISTS `Branch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `Branch` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(10) DEFAULT NULL,
  `repo_id` char(41) DEFAULT NULL,
  `commit_id` char(41) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Branch`
--

LOCK TABLES `Branch` WRITE;
/*!40000 ALTER TABLE `Branch` DISABLE KEYS */;
INSERT INTO `Branch` VALUES
(3,'master','53f6484c-be88-4a8e-b9a6-a627acfcf564','4cf0052e4606a47ab8be81561a80c4b6761d5ba7'),
(11,'master','e60d85bc-9452-4eeb-8d93-8e157ef7a5ce','c00a5cdf6e153e6d01f006488e4e43b28851fdc0');
/*!40000 ALTER TABLE `Branch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FileLockTimestamp`
--

DROP TABLE IF EXISTS `FileLockTimestamp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `FileLockTimestamp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(40) DEFAULT NULL,
  `update_time` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FileLockTimestamp`
--

LOCK TABLES `FileLockTimestamp` WRITE;
/*!40000 ALTER TABLE `FileLockTimestamp` DISABLE KEYS */;
/*!40000 ALTER TABLE `FileLockTimestamp` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FileLocks`
--

DROP TABLE IF EXISTS `FileLocks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `FileLocks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(40) NOT NULL,
  `path` text NOT NULL,
  `user_name` varchar(255) NOT NULL,
  `lock_time` bigint(20) DEFAULT NULL,
  `expire` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FileLocks`
--

LOCK TABLES `FileLocks` WRITE;
/*!40000 ALTER TABLE `FileLocks` DISABLE KEYS */;
/*!40000 ALTER TABLE `FileLocks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FolderGroupPerm`
--

DROP TABLE IF EXISTS `FolderGroupPerm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `FolderGroupPerm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) NOT NULL,
  `path` text NOT NULL,
  `permission` char(15) DEFAULT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FolderGroupPerm`
--

LOCK TABLES `FolderGroupPerm` WRITE;
/*!40000 ALTER TABLE `FolderGroupPerm` DISABLE KEYS */;
/*!40000 ALTER TABLE `FolderGroupPerm` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FolderPermTimestamp`
--

DROP TABLE IF EXISTS `FolderPermTimestamp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `FolderPermTimestamp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `timestamp` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FolderPermTimestamp`
--

LOCK TABLES `FolderPermTimestamp` WRITE;
/*!40000 ALTER TABLE `FolderPermTimestamp` DISABLE KEYS */;
/*!40000 ALTER TABLE `FolderPermTimestamp` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `FolderUserPerm`
--

DROP TABLE IF EXISTS `FolderUserPerm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `FolderUserPerm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) NOT NULL,
  `path` text NOT NULL,
  `permission` char(15) DEFAULT NULL,
  `user` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `FolderUserPerm`
--

LOCK TABLES `FolderUserPerm` WRITE;
/*!40000 ALTER TABLE `FolderUserPerm` DISABLE KEYS */;
/*!40000 ALTER TABLE `FolderUserPerm` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `GCID`
--

DROP TABLE IF EXISTS `GCID`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `GCID` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `gc_id` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `GCID`
--

LOCK TABLES `GCID` WRITE;
/*!40000 ALTER TABLE `GCID` DISABLE KEYS */;
INSERT INTO `GCID` VALUES
(1,'53f6484c-be88-4a8e-b9a6-a627acfcf564','e86bb6ac-16ea-4ae1-ae7c-815f34e9af80');
/*!40000 ALTER TABLE `GCID` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `GarbageRepos`
--

DROP TABLE IF EXISTS `GarbageRepos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `GarbageRepos` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `GarbageRepos`
--

LOCK TABLES `GarbageRepos` WRITE;
/*!40000 ALTER TABLE `GarbageRepos` DISABLE KEYS */;
/*!40000 ALTER TABLE `GarbageRepos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `InnerPubRepo`
--

DROP TABLE IF EXISTS `InnerPubRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `InnerPubRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `InnerPubRepo`
--

LOCK TABLES `InnerPubRepo` WRITE;
/*!40000 ALTER TABLE `InnerPubRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `InnerPubRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `LastGCID`
--

DROP TABLE IF EXISTS `LastGCID`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `LastGCID` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `client_id` varchar(128) DEFAULT NULL,
  `gc_id` char(36) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`,`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=791 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `LastGCID`
--

LOCK TABLES `LastGCID` WRITE;
/*!40000 ALTER TABLE `LastGCID` DISABLE KEYS */;
/*!40000 ALTER TABLE `LastGCID` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgDownloadRateLimit`
--

DROP TABLE IF EXISTS `OrgDownloadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgDownloadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `download_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgDownloadRateLimit`
--

LOCK TABLES `OrgDownloadRateLimit` WRITE;
/*!40000 ALTER TABLE `OrgDownloadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgDownloadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgGroupRepo`
--

DROP TABLE IF EXISTS `OrgGroupRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgGroupRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `repo_id` char(37) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`,`group_id`,`repo_id`),
  KEY `repo_id` (`repo_id`),
  KEY `owner` (`owner`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgGroupRepo`
--

LOCK TABLES `OrgGroupRepo` WRITE;
/*!40000 ALTER TABLE `OrgGroupRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgGroupRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgInnerPubRepo`
--

DROP TABLE IF EXISTS `OrgInnerPubRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgInnerPubRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `repo_id` char(37) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`,`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgInnerPubRepo`
--

LOCK TABLES `OrgInnerPubRepo` WRITE;
/*!40000 ALTER TABLE `OrgInnerPubRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgInnerPubRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgQuota`
--

DROP TABLE IF EXISTS `OrgQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgQuota`
--

LOCK TABLES `OrgQuota` WRITE;
/*!40000 ALTER TABLE `OrgQuota` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgRepo`
--

DROP TABLE IF EXISTS `OrgRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `repo_id` char(37) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`,`repo_id`),
  UNIQUE KEY `repo_id` (`repo_id`),
  KEY `org_id_2` (`org_id`,`user`),
  KEY `user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgRepo`
--

LOCK TABLES `OrgRepo` WRITE;
/*!40000 ALTER TABLE `OrgRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgSharedRepo`
--

DROP TABLE IF EXISTS `OrgSharedRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgSharedRepo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `repo_id` char(37) DEFAULT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `to_email` varchar(255) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`),
  KEY `org_id` (`org_id`,`repo_id`),
  KEY `from_email` (`from_email`),
  KEY `to_email` (`to_email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgSharedRepo`
--

LOCK TABLES `OrgSharedRepo` WRITE;
/*!40000 ALTER TABLE `OrgSharedRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgSharedRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgUploadRateLimit`
--

DROP TABLE IF EXISTS `OrgUploadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgUploadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `upload_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgUploadRateLimit`
--

LOCK TABLES `OrgUploadRateLimit` WRITE;
/*!40000 ALTER TABLE `OrgUploadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgUploadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgUserDefaultQuota`
--

DROP TABLE IF EXISTS `OrgUserDefaultQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgUserDefaultQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgUserDefaultQuota`
--

LOCK TABLES `OrgUserDefaultQuota` WRITE;
/*!40000 ALTER TABLE `OrgUserDefaultQuota` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgUserDefaultQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `OrgUserQuota`
--

DROP TABLE IF EXISTS `OrgUserQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `OrgUserQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` int(11) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `org_id` (`org_id`,`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `OrgUserQuota`
--

LOCK TABLES `OrgUserQuota` WRITE;
/*!40000 ALTER TABLE `OrgUserQuota` DISABLE KEYS */;
/*!40000 ALTER TABLE `OrgUserQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Repo`
--

DROP TABLE IF EXISTS `Repo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `Repo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Repo`
--

LOCK TABLES `Repo` WRITE;
/*!40000 ALTER TABLE `Repo` DISABLE KEYS */;
INSERT INTO `Repo` VALUES
(3,'53f6484c-be88-4a8e-b9a6-a627acfcf564'),
(11,'e60d85bc-9452-4eeb-8d93-8e157ef7a5ce');
/*!40000 ALTER TABLE `Repo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoFileCount`
--

DROP TABLE IF EXISTS `RepoFileCount`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoFileCount` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `file_count` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoFileCount`
--

LOCK TABLES `RepoFileCount` WRITE;
/*!40000 ALTER TABLE `RepoFileCount` DISABLE KEYS */;
INSERT INTO `RepoFileCount` VALUES
(1,'53f6484c-be88-4a8e-b9a6-a627acfcf564',2513);
/*!40000 ALTER TABLE `RepoFileCount` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoGroup`
--

DROP TABLE IF EXISTS `RepoGroup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoGroup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_id` (`group_id`,`repo_id`),
  KEY `repo_id` (`repo_id`),
  KEY `user_name` (`user_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoGroup`
--

LOCK TABLES `RepoGroup` WRITE;
/*!40000 ALTER TABLE `RepoGroup` DISABLE KEYS */;
/*!40000 ALTER TABLE `RepoGroup` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoHead`
--

DROP TABLE IF EXISTS `RepoHead`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoHead` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `branch_name` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoHead`
--

LOCK TABLES `RepoHead` WRITE;
/*!40000 ALTER TABLE `RepoHead` DISABLE KEYS */;
INSERT INTO `RepoHead` VALUES
(3,'53f6484c-be88-4a8e-b9a6-a627acfcf564','master'),
(11,'e60d85bc-9452-4eeb-8d93-8e157ef7a5ce','master');
/*!40000 ALTER TABLE `RepoHead` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoHistoryLimit`
--

DROP TABLE IF EXISTS `RepoHistoryLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoHistoryLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `days` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoHistoryLimit`
--

LOCK TABLES `RepoHistoryLimit` WRITE;
/*!40000 ALTER TABLE `RepoHistoryLimit` DISABLE KEYS */;
INSERT INTO `RepoHistoryLimit` VALUES
(1,'53f6484c-be88-4a8e-b9a6-a627acfcf564',60);
/*!40000 ALTER TABLE `RepoHistoryLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoInfo`
--

DROP TABLE IF EXISTS `RepoInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoInfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `update_time` bigint(20) DEFAULT NULL,
  `version` int(11) DEFAULT NULL,
  `is_encrypted` int(11) DEFAULT NULL,
  `last_modifier` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 0,
  `type` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoInfo`
--

LOCK TABLES `RepoInfo` WRITE;
/*!40000 ALTER TABLE `RepoInfo` DISABLE KEYS */;
INSERT INTO `RepoInfo` VALUES
(3,'53f6484c-be88-4a8e-b9a6-a627acfcf564','Data',1751817956,1,0,'<EMAIL>',0,NULL),
(11,'e60d85bc-9452-4eeb-8d93-8e157ef7a5ce','My Library Template',1751361903,1,0,'system',0,NULL);
/*!40000 ALTER TABLE `RepoInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoOwner`
--

DROP TABLE IF EXISTS `RepoOwner`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoOwner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `owner_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`),
  KEY `owner_id` (`owner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoOwner`
--

LOCK TABLES `RepoOwner` WRITE;
/*!40000 ALTER TABLE `RepoOwner` DISABLE KEYS */;
INSERT INTO `RepoOwner` VALUES
(3,'53f6484c-be88-4a8e-b9a6-a627acfcf564','<EMAIL>'),
(11,'e60d85bc-9452-4eeb-8d93-8e157ef7a5ce','System');
/*!40000 ALTER TABLE `RepoOwner` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoSize`
--

DROP TABLE IF EXISTS `RepoSize`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoSize` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `size` bigint(20) unsigned DEFAULT NULL,
  `head_id` char(41) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoSize`
--

LOCK TABLES `RepoSize` WRITE;
/*!40000 ALTER TABLE `RepoSize` DISABLE KEYS */;
INSERT INTO `RepoSize` VALUES
(1,'53f6484c-be88-4a8e-b9a6-a627acfcf564',565547771,'4cf0052e4606a47ab8be81561a80c4b6761d5ba7');
/*!40000 ALTER TABLE `RepoSize` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoStorageId`
--

DROP TABLE IF EXISTS `RepoStorageId`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoStorageId` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(40) NOT NULL,
  `storage_id` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoStorageId`
--

LOCK TABLES `RepoStorageId` WRITE;
/*!40000 ALTER TABLE `RepoStorageId` DISABLE KEYS */;
/*!40000 ALTER TABLE `RepoStorageId` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoSyncError`
--

DROP TABLE IF EXISTS `RepoSyncError`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoSyncError` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `token` char(41) DEFAULT NULL,
  `error_time` bigint(20) unsigned DEFAULT NULL,
  `error_con` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoSyncError`
--

LOCK TABLES `RepoSyncError` WRITE;
/*!40000 ALTER TABLE `RepoSyncError` DISABLE KEYS */;
/*!40000 ALTER TABLE `RepoSyncError` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoTokenPeerInfo`
--

DROP TABLE IF EXISTS `RepoTokenPeerInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoTokenPeerInfo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `token` char(41) DEFAULT NULL,
  `peer_id` char(41) DEFAULT NULL,
  `peer_ip` varchar(50) DEFAULT NULL,
  `peer_name` varchar(255) DEFAULT NULL,
  `sync_time` bigint(20) DEFAULT NULL,
  `client_ver` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `peer_id` (`peer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoTokenPeerInfo`
--

LOCK TABLES `RepoTokenPeerInfo` WRITE;
/*!40000 ALTER TABLE `RepoTokenPeerInfo` DISABLE KEYS */;
INSERT INTO `RepoTokenPeerInfo` VALUES
(1,'e4b2da1ca14a4f836c76915c66c16e6c01e52d6f','29d531e7dc1f68ccda3bf49026af6099b5c57aac','************','Anbool-Mini',1751535242,NULL),
(2,'80a4e513c2ef45d7b05d7ba55a543dcecfac5163','f0383e6331785c2989015d54d51fdd5825f15a4b','*************','Anbool-Mac',1751809368,NULL);
/*!40000 ALTER TABLE `RepoTokenPeerInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoTrash`
--

DROP TABLE IF EXISTS `RepoTrash`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoTrash` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `repo_name` varchar(255) DEFAULT NULL,
  `head_id` char(40) DEFAULT NULL,
  `owner_id` varchar(255) DEFAULT NULL,
  `size` bigint(20) DEFAULT NULL,
  `org_id` int(11) DEFAULT NULL,
  `del_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`),
  KEY `owner_id` (`owner_id`),
  KEY `org_id` (`org_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoTrash`
--

LOCK TABLES `RepoTrash` WRITE;
/*!40000 ALTER TABLE `RepoTrash` DISABLE KEYS */;
/*!40000 ALTER TABLE `RepoTrash` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoUserToken`
--

DROP TABLE IF EXISTS `RepoUserToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoUserToken` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `token` char(41) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`,`token`),
  KEY `token` (`token`),
  KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoUserToken`
--

LOCK TABLES `RepoUserToken` WRITE;
/*!40000 ALTER TABLE `RepoUserToken` DISABLE KEYS */;
INSERT INTO `RepoUserToken` VALUES
(1,'53f6484c-be88-4a8e-b9a6-a627acfcf564','<EMAIL>','e4b2da1ca14a4f836c76915c66c16e6c01e52d6f'),
(2,'53f6484c-be88-4a8e-b9a6-a627acfcf564','<EMAIL>','80a4e513c2ef45d7b05d7ba55a543dcecfac5163');
/*!40000 ALTER TABLE `RepoUserToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RepoValidSince`
--

DROP TABLE IF EXISTS `RepoValidSince`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RepoValidSince` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `timestamp` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RepoValidSince`
--

LOCK TABLES `RepoValidSince` WRITE;
/*!40000 ALTER TABLE `RepoValidSince` DISABLE KEYS */;
INSERT INTO `RepoValidSince` VALUES
(82,'53f6484c-be88-4a8e-b9a6-a627acfcf564',1746648903);
/*!40000 ALTER TABLE `RepoValidSince` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RoleDownloadRateLimit`
--

DROP TABLE IF EXISTS `RoleDownloadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RoleDownloadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role` varchar(255) DEFAULT NULL,
  `download_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RoleDownloadRateLimit`
--

LOCK TABLES `RoleDownloadRateLimit` WRITE;
/*!40000 ALTER TABLE `RoleDownloadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `RoleDownloadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RoleQuota`
--

DROP TABLE IF EXISTS `RoleQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RoleQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role` varchar(255) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RoleQuota`
--

LOCK TABLES `RoleQuota` WRITE;
/*!40000 ALTER TABLE `RoleQuota` DISABLE KEYS */;
/*!40000 ALTER TABLE `RoleQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `RoleUploadRateLimit`
--

DROP TABLE IF EXISTS `RoleUploadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `RoleUploadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `role` varchar(255) DEFAULT NULL,
  `upload_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `RoleUploadRateLimit`
--

LOCK TABLES `RoleUploadRateLimit` WRITE;
/*!40000 ALTER TABLE `RoleUploadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `RoleUploadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SeafileConf`
--

DROP TABLE IF EXISTS `SeafileConf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `SeafileConf` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `cfg_group` varchar(255) NOT NULL,
  `cfg_key` varchar(255) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `property` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SeafileConf`
--

LOCK TABLES `SeafileConf` WRITE;
/*!40000 ALTER TABLE `SeafileConf` DISABLE KEYS */;
/*!40000 ALTER TABLE `SeafileConf` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SharedRepo`
--

DROP TABLE IF EXISTS `SharedRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `SharedRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `to_email` varchar(255) DEFAULT NULL,
  `permission` char(15) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`),
  KEY `from_email` (`from_email`),
  KEY `to_email` (`to_email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SharedRepo`
--

LOCK TABLES `SharedRepo` WRITE;
/*!40000 ALTER TABLE `SharedRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `SharedRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `SystemInfo`
--

DROP TABLE IF EXISTS `SystemInfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `SystemInfo` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `info_key` varchar(256) DEFAULT NULL,
  `info_value` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `SystemInfo`
--

LOCK TABLES `SystemInfo` WRITE;
/*!40000 ALTER TABLE `SystemInfo` DISABLE KEYS */;
INSERT INTO `SystemInfo` VALUES
(2,'default_repo_id','e60d85bc-9452-4eeb-8d93-8e157ef7a5ce');
/*!40000 ALTER TABLE `SystemInfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserDownloadRateLimit`
--

DROP TABLE IF EXISTS `UserDownloadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `UserDownloadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `download_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserDownloadRateLimit`
--

LOCK TABLES `UserDownloadRateLimit` WRITE;
/*!40000 ALTER TABLE `UserDownloadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `UserDownloadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserQuota`
--

DROP TABLE IF EXISTS `UserQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `UserQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user` (`user`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserQuota`
--

LOCK TABLES `UserQuota` WRITE;
/*!40000 ALTER TABLE `UserQuota` DISABLE KEYS */;
INSERT INTO `UserQuota` VALUES
(3,'<EMAIL>',100000000000000000);
/*!40000 ALTER TABLE `UserQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserShareQuota`
--

DROP TABLE IF EXISTS `UserShareQuota`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `UserShareQuota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `quota` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserShareQuota`
--

LOCK TABLES `UserShareQuota` WRITE;
/*!40000 ALTER TABLE `UserShareQuota` DISABLE KEYS */;
/*!40000 ALTER TABLE `UserShareQuota` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `UserUploadRateLimit`
--

DROP TABLE IF EXISTS `UserUploadRateLimit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `UserUploadRateLimit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user` varchar(255) DEFAULT NULL,
  `upload_limit` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `UserUploadRateLimit`
--

LOCK TABLES `UserUploadRateLimit` WRITE;
/*!40000 ALTER TABLE `UserUploadRateLimit` DISABLE KEYS */;
/*!40000 ALTER TABLE `UserUploadRateLimit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `VirtualRepo`
--

DROP TABLE IF EXISTS `VirtualRepo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `VirtualRepo` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(36) DEFAULT NULL,
  `origin_repo` char(36) DEFAULT NULL,
  `path` text DEFAULT NULL,
  `base_commit` char(40) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`),
  KEY `origin_repo` (`origin_repo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `VirtualRepo`
--

LOCK TABLES `VirtualRepo` WRITE;
/*!40000 ALTER TABLE `VirtualRepo` DISABLE KEYS */;
/*!40000 ALTER TABLE `VirtualRepo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `WebAP`
--

DROP TABLE IF EXISTS `WebAP`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `WebAP` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(37) DEFAULT NULL,
  `access_property` char(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `WebAP`
--

LOCK TABLES `WebAP` WRITE;
/*!40000 ALTER TABLE `WebAP` DISABLE KEYS */;
/*!40000 ALTER TABLE `WebAP` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `WebUploadTempFiles`
--

DROP TABLE IF EXISTS `WebUploadTempFiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `WebUploadTempFiles` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `repo_id` char(40) NOT NULL,
  `file_path` text NOT NULL,
  `tmp_file_path` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `repo_id` (`repo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `WebUploadTempFiles`
--

LOCK TABLES `WebUploadTempFiles` WRITE;
/*!40000 ALTER TABLE `WebUploadTempFiles` DISABLE KEYS */;
/*!40000 ALTER TABLE `WebUploadTempFiles` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-06 20:20:02
