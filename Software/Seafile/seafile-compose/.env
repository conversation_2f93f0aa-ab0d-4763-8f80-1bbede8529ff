COMPOSE_FILE='seafile-server.yml,seasearch.yml,seadoc.yml,notification-server.yml'
COMPOSE_PATH_SEPARATOR=','

# Seafile & Basic services
## Images
SEAFILE_IMAGE=seafileltd/seafile-pro-mc:12.0.14
SEAFILE_DB_IMAGE=mariadb:10.11
SEAFILE_MEMCACHED_IMAGE=memcached:1.6.38

## Persistent Storage
SEAFILE_VOLUME=/data/apps/seafile/server
SEAFILE_MYSQL_VOLUME=/data/apps/seafile/mariadb

## Database
SEAFILE_MYSQL_DB_HOST=seafile-db
SEAFILE_MYSQL_DB_USER=seafile
SEAFILE_MYSQL_DB_PASSWORD=XAVbWwARqTDKH9aCYYTtuLiAPhQQYW8seNFv8QzrNw62sDrALB66BE9kYVMFYvwi

### Database root password
INIT_SEAFILE_MYSQL_ROOT_PASSWORD=uBYiinbMCQXcysqHQZdfhGJnDaNUB2dNj2nhp6oe6CBxhwVLzPXhXRumdhPmHTeV

## Scheme
SEAFILE_SERVER_HOSTNAME=box.lilh.net
SEAFILE_SERVER_PROTOCOL=https

## Startup parameters
TIME_ZONE=Asia/Shanghai
JWT_PRIVATE_KEY=etEUUEsBbuByFtLuWskBh6YWpsZnFtDiUoFBfkMQT2JNUEBYz6sW8TJUs9tKnYti

## Initial variables (can be removed after firstime startup)

### Seafile admin user
INIT_SEAFILE_ADMIN_EMAIL=<EMAIL>
INIT_SEAFILE_ADMIN_PASSWORD=6N9oBkURCVEk6WyRYZGcMLxudtQLixVZffDrePD6Nduubt2tYEJywUfWmebu6Gb6

### S3
INIT_S3_STORAGE_BACKEND_CONFIG=true
INIT_S3_COMMIT_BUCKET=seafile-commits-1257719787
INIT_S3_FS_BUCKET=seafile-fs-1257719787
INIT_S3_BLOCK_BUCKET=seafile-blocks-1257719787
INIT_S3_KEY_ID=AKIDITSjiimjXFl5FUiqsrnePJi5Pg7afq0K
INIT_S3_SECRET_KEY=TwLkKDIXm7V2UxV23LpfgVDtaL7p4VuQ
INIT_S3_USE_V4_SIGNATURE=true
INIT_S3_AWS_REGION=ap-beijing
INIT_S3_HOST=cos.ap-beijing.myqcloud.com
INIT_S3_USE_HTTPS=false

# SeaDoc
ENABLE_SEADOC=true
SEADOC_IMAGE=seafileltd/sdoc-server:1.0.5
SEADOC_VOLUME=/data/apps/seafile/seadoc/data
SEADOC_SERVER_URL=https://box.lilh.net/sdoc-server

# SeaSearch
## Image
SEASEARCH_IMAGE=seafileltd/seasearch:0.9.1

## Storage
SS_STORAGE_TYPE=disk # disk (local storage), s3, oss

### Local storage mode
SS_DATA_PATH=/data/apps/seafile/seasearch/data # Persistent storage path
SS_MAX_OBJ_CACHE_SIZE=10GB

### S3 mode
#SS_S3_USE_V4_SIGNATURE=false
#SS_S3_ACCESS_ID=<your access id>
#SS_S3_ACCESS_SECRET=<your access secret>
#SS_S3_ENDPOINT=s3.us-east-1.amazonaws.com
#SS_S3_BUCKET=<your bucket name>
#SS_S3_USE_HTTPS=true
#SS_S3_PATH_STYLE_REQUEST=true
#SS_S3_AWS_REGION=us-east-1
#SS_S3_SSE_C_KEY=<your SSE-C key>

## Log
SS_LOG_TO_STDOUT=false
SS_LOG_OUTPUT=true
SS_LOG_LEVEL=info
SS_LOG_DIR=/data/apps/seafile/seasearch/log

## Initial variables (can be removed after firstime startup SeaSearch service)
INIT_SS_ADMIN_USER=anbool
INIT_SS_ADMIN_PASSWORD=AnJmbjvfke6a7oNV

# Notification
NOTIFICATION_SERVER_IMAGE=seafileltd/notification-server:12.0.11
NOTIFICATION_SERVER_VOLUME=/data/apps/seafile/notification/data
