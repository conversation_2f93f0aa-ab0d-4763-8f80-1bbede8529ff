services:
  seafile-seasearch:
    image: ${SEASEARCH_IMAGE:-seafileltd/seasearch:latest}
    restart: unless-stopped
    container_name: seafile-seasearch
    volumes:
      - ${SS_DATA_PATH:-/opt/seasearch-data}:/opt/seasearch/data
    environment:
      - ZINC_FIRST_ADMIN_USER=${INIT_SS_ADMIN_USER:-}
      - ZINC_FIRST_ADMIN_PASSWORD=${INIT_SS_ADMIN_PASSWORD:-}
      - GIN_MODE=${GIN_MODE:-release}
      - ZINC_WAL_ENABLE=${SS_WAL_ENABLE:-true}
      - ZINC_STORAGE_TYPE=${SS_STORAGE_TYPE:-}
      - ZINC_SHARD_NUM=${SS_SHARD_NUM:-}
      - ZINC_MAX_OBJ_CACHE_SIZE=${SS_MAX_OBJ_CACHE_SIZE:-10GB}
      - ZINC_S3_ACCESS_ID=${SS_S3_ACCESS_ID:-}
      - ZINC_S3_USE_V4_SIGNATURE=${SS_S3_USE_V4_SIGNATURE:-false}
      - ZINC_S3_ACCESS_SECRET=${SS_S3_ACCESS_SECRET:-}
      - ZINC_S3_ENDPOINT=${SS_S3_ENDPOINT:-s3.us-east-1.amazonaws.com}
      - ZINC_S3_BUCKET=${SS_S3_BUCKET:-}
      - ZINC_S3_USE_HTTPS=${SS_S3_USE_HTTPS:-true}
      - ZINC_S3_PATH_STYLE_REQUEST=${SS_S3_PATH_STYLE_REQUEST:-true}
      - ZINC_S3_AWS_REGION=${SS_S3_AWS_REGION:-us-east-1}
      - ZINC_S3_SSE_C_KEY=${SS_S3_SSE_C_KEY:-}
      - ZINC_SERVER_MODE=${SS_SERVER_MODE:-}
      - ZINC_CLUSTER_ID=${SS_CLUSTER_ID:-}
      - ZINC_ETCD_USERNAME=${SS_ETCD_USERNAME:-}
      - ZINC_ETCD_PASSWORD=${SS_ETCD_PASSWORD:-}
      - ZINC_CLUSTER_PROXY_LOG_DIR=${SS_CLUSTER_PROXY_LOG_DIR:-/opt/seasearch/data/log}
      - ZINC_CLUSTER_PROXY_HOST=${SS_CLUSTER_PROXY_HOST:-0.0.0.0}
      - ZINC_CLUSTER_PROXY_PORT=${SS_CLUSTER_PROXY_PORT:-4082}
      - ZINC_ETCD_ENDPOINTS=${SS_ETCD_ENDPOINTS:-127.0.0.1:2379}
      - ZINC_ETCD_PREFIX=${SS_ETCD_PREFIX:-/zinc}
      - ZINC_MAX_DOCUMENT_SIZE=${SS_MAX_DOCUMENT_SIZE:-1m}
      - ZINC_CLUSTER_MANAGER_ADDR=${SS_CLUSTER_MANAGER_ADDR:-127.0.0.1:4081}
      - ZINC_CLUSTER_MANAGER_LOG_DIR=${SS_CLUSTER_MANAGER_LOG_DIR:-/opt/seasearch/data/log}
      - ZINC_CLUSTER_MANAGER_HOST=${SS_CLUSTER_MANAGER_HOST:-0.0.0.0}
      - ZINC_CLUSTER_MANAGER_PORT=${SS_CLUSTER_MANAGER_PORT:-4081}
      - ZINC_CLUSTER_MANAGER_ETCD_ENDPOINTS=${SS_CLUSTER_MANAGER_ETCD_ENDPOINTS:-127.0.0.1:2379}
      - ZINC_CLUSTER_MANAGER_ETCD_PREFIX=${SS_CLUSTER_MANAGER_ETCD_PREFIX:-/zinc}
      - SS_LOG_TO_STDOUT=${SS_LOG_TO_STDOUT:-false}
      - ZINC_LOG_OUTPUT=${SS_LOG_OUTPUT:-true}
      - ZINC_LOG_DIR=${SS_LOG_DIR:-/opt/seasearch/data/log}
      - ZINC_LOG_LEVEL=${SS_LOG_LEVEL:-debug}
      - ZINC_PLUGIN_GSE_ENABLE=${SS_PLUGIN_GSE_ENABLE:-false}
      - ZINC_PLUGIN_GSE_DICT_EMBED=${SS_PLUGIN_GSE_DICT_EMBED:-}
      - ZINC_PLUGIN_GSE_DICT_PATH=${SS_PLUGIN_GSE_DICT_PATH:-}
    networks:
      - seafile-net


networks:
  seafile-net:
    name: seafile-net
