### Run ###
```bash
mkdir -p /data/scripts && mkdir -p /data/scripts/docker_db && cd /data/scripts/docker_db && curl -# -O https://mirror.lilh.net/priv/DockerDB/dump.sh && chmod +x dump.sh && ls
```
```bash
./dump.sh
```

### Update ###
```bash
cd /data/scripts/docker_db && rm -rf dump.sh && curl -# -O https://mirror.lilh.net/priv/DockerDB/dump.sh && chmod +x dump.sh && ls
```

### Cron ###
#### 每天 04:20 执行 ####
```bash
20 4 * * * cd /data/scripts/docker_db && ./dump.sh
```