# 数据库备份脚本使用指南

## 概述

`Linux/DockerDB/dump.sh` 脚本支持对 Docker 容器中的多种数据库进行自动备份，包括 MySQL/MariaDB、PostgreSQL 和 MongoDB。

## 功能特性

### ✅ 支持的数据库类型
- **MySQL/MariaDB**: 支持有密码和无密码连接
- **PostgreSQL**: 支持密码认证和信任认证
- **MongoDB**: 支持用户认证和无认证模式

### ✅ 无密码连接处理
脚本会自动检测密码字段是否为空，并采用相应的连接方式：

#### MySQL/MariaDB
- **有密码**: `mysqldump -u"用户" -p"密码" --opt 数据库`
- **无密码**: `mysqldump -u"用户" --opt 数据库`

#### PostgreSQL  
- **有密码**: `PGPASSWORD='密码' pg_dump -U 用户 -d 数据库`
- **无密码**: `pg_dump -U 用户 -d 数据库` (需要信任认证)

#### MongoDB
- **有密码**: `mongodump --username='用户' --password='密码' --db='数据库'`
- **无密码**: `mongodump --db='数据库'` (需要无认证模式)

## 配置说明

### 配置格式
```bash
"TYPE,DOCKER_CONTAINER,DB_USER,DB_PASSWORD,DATABASES,BACKUP_PATH"
```

### 参数说明
- `TYPE`: 数据库类型 (mysql|postgresql|mongodb)
- `DOCKER_CONTAINER`: Docker 容器名称
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码 (**留空表示无密码连接**)
- `DATABASES`: 数据库名称，多个用分号分隔
- `BACKUP_PATH`: 备份文件存储路径

### 配置示例

#### 有密码配置
```bash
CONFIGS=(
    "mysql,seafile-mariadb,root,MyPassword123,ccnet_db;seafile_db,/data/backup/mysql"
    "postgresql,postgres-container,postgres,PgPassword,appdb,/data/backup/postgres"
    "mongodb,mongo-container,admin,MongoPass,userdb,/data/backup/mongo"
)
```

#### 无密码配置
```bash
CONFIGS=(
    "mysql,mysql-nopass,root,,testdb,/data/backup/mysql-nopass"
    "postgresql,postgres-trust,postgres,,appdb,/data/backup/postgres-trust"  
    "mongodb,mongo-noauth,,,logdb,/data/backup/mongo-noauth"
)
```

## 数据库无密码配置要求

### MySQL/MariaDB 无密码设置
1. 创建无密码用户或修改现有用户：
```sql
-- 创建无密码用户
CREATE USER 'backup_user'@'%';
GRANT SELECT, LOCK TABLES, SHOW VIEW ON *.* TO 'backup_user'@'%';

-- 或修改现有用户移除密码
ALTER USER 'root'@'%' IDENTIFIED BY '';
```

### PostgreSQL 信任认证设置
修改 `pg_hba.conf` 文件：
```
# 允许本地信任连接
local   all             postgres                                trust
host    all             postgres        127.0.0.1/32            trust
```

### MongoDB 无认证模式
启动 MongoDB 时不启用认证：
```bash
mongod --noauth
```

## 安全建议

### 🔒 密码安全
- 避免在脚本中硬编码密码
- 考虑使用环境变量或配置文件
- 定期轮换数据库密码

### 🛡️ 权限控制
- 为备份创建专用的只读用户
- 限制备份用户的网络访问
- 定期审查用户权限

### 📁 备份文件安全
- 设置适当的文件权限 (600 或 640)
- 考虑加密敏感的备份文件
- 定期清理旧的备份文件

## 故障排除

### 常见错误及解决方案

#### MySQL 错误
- **错误**: `Access denied for user`
- **解决**: 检查用户权限和密码设置

#### PostgreSQL 错误  
- **错误**: `FATAL: password authentication failed`
- **解决**: 检查 `pg_hba.conf` 配置或密码设置

#### MongoDB 错误
- **错误**: `Authentication failed`
- **解决**: 检查认证配置或用户权限

### 调试技巧
1. 查看详细日志：`tail -f dump.log`
2. 手动测试连接：
```bash
# MySQL
docker exec -it container_name mysql -u user -p

# PostgreSQL  
docker exec -it container_name psql -U user -d database

# MongoDB
docker exec -it container_name mongo --username user
```

## 最佳实践

1. **定期测试**: 定期验证备份文件的完整性
2. **监控空间**: 监控备份目录的磁盘空间使用
3. **自动化**: 使用 cron 定时执行备份脚本
4. **版本控制**: 保留多个版本的备份文件
5. **异地备份**: 将重要备份复制到远程位置

## 使用示例

```bash
# 执行备份
./dump.sh

# 查看日志
cat dump.log

# 验证备份文件
ls -la /data/backup/*/
```
