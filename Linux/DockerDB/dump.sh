#!/bin/bash

set -euo pipefail
IFS=$'\n\t'

# -----------------------------------------------------------------------------
# 使用方法
# -----------------------------------------------------------------------------
usage() {
    cat <<EOF
Usage: $0

脚本会读取 CONFIGS 数组并对每一项进行不同 DB 类型的转储
EOF
    exit 1
}

# -----------------------------------------------------------------------------
# 参数解析 (无参数)
# -----------------------------------------------------------------------------
if [[ $# -gt 0 ]]; then
    usage
fi

# -----------------------------------------------------------------------------
# 日志函数
# -----------------------------------------------------------------------------
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "./dump.log"
}

log "----------------------------"

# -----------------------------------------------------------------------------
# 通用配置数组
# 格式:
#   "TYPE,DOCKER_CONTAINER,DB_USER,DB_PASSWORD,DATABASES,BACKUP_PATH"
# TYPE: mysql | postgresql | mongodb
# DATABASES: 分号分隔 db1;db2;...
# DB_PASSWORD: 留空表示无密码连接
# -----------------------------------------------------------------------------
CONFIGS=(
    #"mysql,seafile-mariadb,root,uBYiinbMCQXcysqHQZdfhGJnDaNUB2dNj2nhp6oe6CBxhwVLzPXhXRumdhPmHTeV,ccnet_db;seafile_db;seahub_db,/data/apps/seafile/backup/mariadb"
    #"mysql,seatable-mariadb,root,Wiux2muD7s76BFighaFnQ4rHPspq6CKwXVUTNXABnrWEKp2WEUY8JGoomVmfxq2z,ccnet_db;seafile_db;dtable_db;scheduler,/data/apps/seatable/backup/mariadb"
    #"postgresql,postgres_container,postgres,MyPgPwd,appdb;analytics,/data/apps/postgresql"
    #"mongodb,mongo_container,admin,MyMongoPwd,users;logs,/data/apps/mongo"

    # 无密码配置示例 (将相关字段留空):
    #"mysql,mysql_nopass_container,root,,testdb,/data/backup/mysql_nopass"
    #"postgresql,postgres_trust_container,postgres,,appdb,/data/backup/postgres_trust"
    #"mongodb,mongo_noauth_container,,,testdb,/data/backup/mongo_noauth"
)

# -----------------------------------------------------------------------------
# 遍历配置并执行转储任务
# -----------------------------------------------------------------------------
for cfg in "${CONFIGS[@]}"; do
    IFS=',' read -r TYPE CONTAINER USER PASS DBS BACKUP_PATH <<<"$cfg"

    # 确保转储目录存在
    mkdir -p "$BACKUP_PATH"

    # 根据类型打印启动日志
    case "$TYPE" in
        mysql)
            log "开始执行 MySQL 转储任务"
            # 选择可用的 dump 工具
            if docker exec -i "$CONTAINER" mariadb-dump --version >/dev/null 2>&1; then
                DUMP_CMD="mariadb-dump"
            elif docker exec -i "$CONTAINER" mysqldump --version >/dev/null 2>&1; then
                DUMP_CMD="mysqldump"
            else
                log "ERROR: 容器中无可用的 MySQL 导出工具"
                continue
            fi
            ;;
        postgresql)
            log "开始执行 PostgreSQL 转储任务"
            ;;
        mongodb)
            log "开始执行 MongoDB 转储任务"
            ;;
        *)
            log "WARN: 已跳过未知数据库类型: $TYPE"
            continue
            ;;
    esac

    IFS=';' read -ra DB_ARRAY <<<"$DBS"
    for db in "${DB_ARRAY[@]}"; do
        # 构建文件路径
        if [[ "$TYPE" == "mongodb" ]]; then
            FILE_PATH="${BACKUP_PATH}/${db}.archive"
        else
            FILE_PATH="${BACKUP_PATH}/${db}.sql"
        fi

        # 删除旧的转储文件
        if [[ -f "$FILE_PATH" ]]; then
            rm -f "$FILE_PATH"
            log "已删除旧的转储文件: $FILE_PATH"
        fi

        # 执行转储
        log "开始转储 [${CONTAINER}] 容器的 [${db}] 数据库"
        case "$TYPE" in
            mysql)
                # 根据密码是否为空选择不同的连接方式
                if [[ -n "$PASS" ]]; then
                    log "使用密码认证连接 MySQL"
                    if docker exec -i "$CONTAINER" $DUMP_CMD -u"$USER" -p"$PASS" --opt "$db" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: MySQL 密码认证转储失败: $FILE_PATH"
                    fi
                else
                    log "使用无密码连接 MySQL"
                    if docker exec -i "$CONTAINER" $DUMP_CMD -u"$USER" --opt "$db" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: MySQL 无密码转储失败: $FILE_PATH (请检查用户权限)"
                    fi
                fi
                ;;

            postgresql)
                # 根据密码是否为空选择不同的连接方式
                if [[ -n "$PASS" ]]; then
                    log "使用密码认证连接 PostgreSQL"
                    if docker exec -i "$CONTAINER" bash -c "export PGPASSWORD='$PASS' && pg_dump -U '$USER' -d '$db' --format=plain" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: PostgreSQL 密码认证转储失败: $FILE_PATH"
                    fi
                else
                    log "使用信任认证连接 PostgreSQL"
                    if docker exec -i "$CONTAINER" bash -c "pg_dump -U '$USER' -d '$db' --format=plain" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: PostgreSQL 信任认证转储失败: $FILE_PATH (请检查 pg_hba.conf 配置)"
                    fi
                fi
                ;;

            mongodb)
                # 根据密码是否为空选择不同的连接方式
                if [[ -n "$PASS" ]]; then
                    log "使用用户认证连接 MongoDB"
                    if docker exec -i "$CONTAINER" bash -c "mongodump --username='$USER' --password='$PASS' --db='$db' --authenticationDatabase='admin' --archive" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: MongoDB 用户认证转储失败: $FILE_PATH"
                    fi
                else
                    log "使用无认证连接 MongoDB"
                    if docker exec -i "$CONTAINER" bash -c "mongodump --db='$db' --archive" > "$FILE_PATH"; then
                        log "转储已完成: $FILE_PATH"
                    else
                        log "ERROR: MongoDB 无认证转储失败: $FILE_PATH (请检查 MongoDB 认证配置)"
                    fi
                fi
                ;;
        esac
    done
done

# 记录转储完成
log "所有数据库转储任务已完成!"
log "----------------------------"